﻿@page "/"
@inject ApiService ApiService
@inject AuthenticationService AuthService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>Dashboard - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12">
            <h1 class="h3 mb-3">
                <i class="fas fa-tachometer-alt me-2"></i>
                Dashboard
            </h1>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Toplam Kategori
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@kategoriSayisi</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tags fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Toplam Firma
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@firmaSayisi</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-building fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Toplam Ürün
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@urunSayisi</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-box fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Toplam Haber
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">@haberSayisi</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-newspaper fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-chart-pie me-2"></i>
                        Hızlı İşlemler
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-6 mb-3">
                            <a href="/kategoriler/ekle" class="btn btn-primary w-100">
                                <i class="fas fa-plus me-2"></i>
                                Kategori Ekle
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="/firmalar/ekle" class="btn btn-success w-100">
                                <i class="fas fa-plus me-2"></i>
                                Firma Ekle
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="/urunler/ekle" class="btn btn-info w-100">
                                <i class="fas fa-plus me-2"></i>
                                Ürün Ekle
                            </a>
                        </div>
                        <div class="col-6 mb-3">
                            <a href="/haberler/ekle" class="btn btn-warning w-100">
                                <i class="fas fa-plus me-2"></i>
                                Haber Ekle
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6 mb-4">
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-info-circle me-2"></i>
                        Sistem Bilgileri
                    </h6>
                </div>
                <div class="card-body">
                    <p><strong>Sistem:</strong> Boykot Uygulaması Admin Paneli</p>
                    <p><strong>Versiyon:</strong> 1.0.0</p>
                    <p><strong>Son Güncelleme:</strong> @DateTime.Now.ToString("dd.MM.yyyy")</p>
                    <p><strong>API URL:</strong> http://localhost:5130</p>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private int kategoriSayisi = 0;
    private int firmaSayisi = 0;
    private int urunSayisi = 0;
    private int haberSayisi = 0;

    protected override async Task OnInitializedAsync()
    {
        if (!AuthService.IsAuthenticated)
        {
            Navigation.NavigateTo("/login");
            return;
        }

        await LoadStatistics();
    }

    private async Task LoadStatistics()
    {
        try
        {
            var kategoriler = await ApiService.GetKategorilerAsync();
            kategoriSayisi = kategoriler.Count;

            var firmalar = await ApiService.GetFirmalarAsync();
            firmaSayisi = firmalar.Count;

            var urunler = await ApiService.GetUrunlerAsync();
            urunSayisi = urunler.Count;

            var haberler = await ApiService.GetHaberlerAsync();
            haberSayisi = haberler.Count;
        }
        catch (Exception ex)
        {
            // Hata durumunda varsayılan değerler kalacak
            Console.WriteLine($"Dashboard istatistikleri yüklenirken hata: {ex.Message}");
        }
    }
}
