using System.ComponentModel.DataAnnotations;

namespace BoycottAppAdminPanel.Models
{
    public class Admin
    {
        public int Id { get; set; }
        public string KullaniciAdi { get; set; } = string.Empty;
        public string Sifre { get; set; } = string.Empty;
    }

    public class LoginRequest
    {
        [Required(ErrorMessage = "Kullanıcı adı gereklidir")]
        public string KullaniciAdi { get; set; } = string.Empty;

        [Required(ErrorMessage = "Şifre gereklidir")]
        public string Sifre { get; set; } = string.Empty;
    }

    public class LoginResponse
    {
        public string Message { get; set; } = string.Empty;
        public int AdminId { get; set; }
        public string KullaniciAdi { get; set; } = string.Empty;

        // Computed property for compatibility
        public bool Success => !string.IsNullOrEmpty(Message) && Message.Contains("başarılı");
        public Admin? Admin => Success ? new Admin { Id = AdminId, KullaniciAdi = KullaniciAdi } : null;
    }
}
