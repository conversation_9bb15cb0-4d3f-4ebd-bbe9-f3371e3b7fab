@page "/urunler/duzenle/{id:int}"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle><PERSON><PERSON><PERSON><PERSON>le - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2"></i>
                Ürün Düzenle
            </h1>
            <a href="/urunler" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Geri <PERSON>
            </a>
        </div>
    </div>

    @if (isLoadingData)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    }
    else if (urun == null)
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <PERSON><PERSON><PERSON>n bulunamadı.
        </div>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body">
                        <EditForm Model="@urun" OnValidSubmit="@HandleSubmit">
                            <DataAnnotationsValidator />
                            
                            <div class="mb-3">
                                <label for="ad" class="form-label">Ürün Adı <span class="text-danger">*</span></label>
                                <InputText @bind-Value="urun.Ad" 
                                           class="form-control" 
                                           id="ad" 
                                           placeholder="Ürün adını girin" />
                                <ValidationMessage For="@(() => urun.Ad)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="firmaId" class="form-label">Firma <span class="text-danger">*</span></label>
                                <InputSelect @bind-Value="urun.FirmaId" class="form-select" id="firmaId">
                                    <option value="0">Firma seçin...</option>
                                    @foreach (var firma in firmalar)
                                    {
                                        <option value="@firma.Id">@firma.Ad (@firma.Ulke)</option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="@(() => urun.FirmaId)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="kategoriId" class="form-label">Kategori <span class="text-danger">*</span></label>
                                <InputSelect @bind-Value="urun.KategoriId" class="form-select" id="kategoriId">
                                    <option value="0">Kategori seçin...</option>
                                    @foreach (var kategori in kategoriler)
                                    {
                                        <option value="@kategori.Id">@kategori.Ad</option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="@(() => urun.KategoriId)" class="text-danger" />
                            </div>

                            @if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    @errorMessage
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(successMessage))
                            {
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    @successMessage
                                </div>
                            }

                            <div class="d-flex justify-content-end gap-2">
                                <a href="/urunler" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    İptal
                                </a>
                                <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                    @if (isLoading)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Güncelleniyor...</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-save me-2"></i>
                                        <span>Güncelle</span>
                                    }
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int Id { get; set; }
    
    private BoykotUrun? urun;
    private List<BoykotFirma> firmalar = new();
    private List<BoykotKategori> kategoriler = new();
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;
    private bool isLoadingData = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoadingData = true;
        try
        {
            var urunTask = ApiService.GetUrunAsync(Id);
            var firmalarTask = ApiService.GetFirmalarAsync();
            var kategorilerTask = ApiService.GetKategorilerAsync();

            await Task.WhenAll(urunTask, firmalarTask, kategorilerTask);

            urun = await urunTask;
            firmalar = await firmalarTask;
            kategoriler = await kategorilerTask;
        }
        catch (Exception ex)
        {
            errorMessage = $"Veriler yüklenirken hata: {ex.Message}";
        }
        finally
        {
            isLoadingData = false;
        }
    }

    private async Task HandleSubmit()
    {
        if (urun == null) return;

        isLoading = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;
        StateHasChanged();

        try
        {
            if (urun.FirmaId == 0)
            {
                errorMessage = "Lütfen bir firma seçin.";
                return;
            }

            if (urun.KategoriId == 0)
            {
                errorMessage = "Lütfen bir kategori seçin.";
                return;
            }

            var success = await ApiService.UpdateUrunAsync(Id, urun);
            
            if (success)
            {
                successMessage = "Ürün başarıyla güncellendi!";
                
                // 2 saniye sonra ürünler sayfasına yönlendir
                await Task.Delay(2000);
                Navigation.NavigateTo("/urunler");
            }
            else
            {
                errorMessage = "Ürün güncellenirken bir hata oluştu.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Bir hata oluştu: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
