using System.ComponentModel.DataAnnotations;

namespace BoycottAppAdminPanel.Models
{
    public class BoykotUrun
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "<PERSON>r<PERSON>n adı gereklidir")]
        [StringLength(100, ErrorMessage = "Ürün adı en fazla 100 karakter olabilir")]
        public string Ad { get; set; } = string.Empty;

        [Required(ErrorMessage = "Firma seçimi gereklidir")]
        [Range(1, int.MaxValue, ErrorMessage = "Geçerli bir firma seçin")]
        public int FirmaId { get; set; }

        [Required(ErrorMessage = "Kategori seçimi gereklidir")]
        [Range(1, int.MaxValue, ErrorMessage = "Geçerli bir kategori seçin")]
        public int KategoriId { get; set; }

        // Navigation properties (for display purposes)
        public string? FirmaAdi { get; set; }
        public string? KategoriAdi { get; set; }
    }
}
