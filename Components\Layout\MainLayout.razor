﻿@using BoycottAppAdminPanel.Services
@inherits LayoutComponentBase
@inject AuthenticationService AuthService
@inject NavigationManager Navigation
@implements IDisposable

@{
    var currentUrl = Navigation.Uri;
    var isLoginPage = currentUrl.Contains("/login");
}

@if (isLoginPage || AuthService.IsAuthenticated)
{
    @if (isLoginPage)
    {
        @Body
    }
    else
    {
        <div class="page">
            <div class="sidebar">
                <NavMenu />
            </div>

            <main>
                <div class="top-row px-4 d-flex justify-content-between align-items-center">
                    <div>
                        <span class="text-muted">Ho<PERSON> geldiniz, </span>
                        <strong>@AuthService.CurrentAdmin?.KullaniciAdi</strong>
                    </div>
                    <button class="btn btn-outline-danger btn-sm" @onclick="Logout">
                        <i class="fas fa-sign-out-alt me-1"></i>
                        <PERSON><PERSON><PERSON><PERSON><PERSON>
                    </button>
                </div>

                <article class="content px-4">
                    @Body
                </article>
            </main>
        </div>
    }
}
else
{
    <div class="container-fluid vh-100 d-flex align-items-center justify-content-center">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
            <p class="mt-2">Yönlendiriliyor...</p>
        </div>
    </div>
}

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

@code {
    protected override void OnInitialized()
    {
        AuthService.AuthenticationStateChanged += OnAuthenticationStateChanged;

        var currentUrl = Navigation.Uri;
        var isLoginPage = currentUrl.Contains("/login");

        if (!isLoginPage && !AuthService.IsAuthenticated)
        {
            Navigation.NavigateTo("/login");
        }
    }

    private void OnAuthenticationStateChanged()
    {
        InvokeAsync(() =>
        {
            StateHasChanged();

            var currentUrl = Navigation.Uri;
            var isLoginPage = currentUrl.Contains("/login");

            Console.WriteLine($"MainLayout: Auth state changed - Authenticated: {AuthService.IsAuthenticated}, IsLoginPage: {isLoginPage}");

            if (isLoginPage && AuthService.IsAuthenticated)
            {
                Console.WriteLine("MainLayout: Redirecting authenticated user from login to home");
                Navigation.NavigateTo("/", forceLoad: true);
            }
            else if (!isLoginPage && !AuthService.IsAuthenticated)
            {
                Console.WriteLine("MainLayout: Redirecting unauthenticated user to login");
                Navigation.NavigateTo("/login");
            }
        });
    }

    private void Logout()
    {
        AuthService.Logout();
        Navigation.NavigateTo("/login");
    }

    public void Dispose()
    {
        AuthService.AuthenticationStateChanged -= OnAuthenticationStateChanged;
    }
}
