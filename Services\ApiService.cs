using BoycottAppAdminPanel.Models;
using System.Net.Http.Json;
using System.Text.Json;

namespace BoycottAppAdminPanel.Services
{
    public class ApiService
    {
        private readonly HttpClient _httpClient;
        private readonly string _baseUrl = "http://localhost:5130/api";

        public ApiService(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        // Admin Login
        public async Task<LoginResponse> LoginAsync(LoginRequest loginRequest)
        {
            try
            {
                Console.WriteLine($"ApiService: Sending login request to {_baseUrl}/admin/login");
                Console.WriteLine($"ApiService: Request data - User: {loginRequest.KullaniciAdi}, Pass: {loginRequest.Sifre}");

                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/admin/login", loginRequest);
                Console.WriteLine($"ApiService: HTTP Status: {response.StatusCode}");

                if (response.IsSuccessStatusCode)
                {
                    var responseContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"ApiService: Response content: {responseContent}");

                    var result = await response.Content.ReadFromJsonAsync<LoginResponse>();
                    Console.WriteLine($"ApiService: Parsed result - Success: {result?.Success}, Message: {result?.Message}");
                    return result ?? new LoginResponse { Message = "Geçersiz yanıt" };
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    Console.WriteLine($"ApiService: Error response: {errorContent}");
                    return new LoginResponse { Message = $"HTTP {response.StatusCode}: {errorContent}" };
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"ApiService: Exception: {ex}");
                return new LoginResponse { Message = $"Hata: {ex.Message}" };
            }
        }

        // Kategori işlemleri
        public async Task<List<BoykotKategori>> GetKategorilerAsync()
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<List<BoykotKategori>>($"{_baseUrl}/kategoriler");
                return response ?? new List<BoykotKategori>();
            }
            catch
            {
                return new List<BoykotKategori>();
            }
        }

        public async Task<BoykotKategori?> GetKategoriAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<BoykotKategori>($"{_baseUrl}/kategoriler/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> CreateKategoriAsync(BoykotKategori kategori)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/kategoriler", kategori);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateKategoriAsync(int id, BoykotKategori kategori)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/kategoriler/{id}", kategori);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteKategoriAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_baseUrl}/kategoriler/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Firma işlemleri
        public async Task<List<BoykotFirma>> GetFirmalarAsync()
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<List<BoykotFirma>>($"{_baseUrl}/firmalar");
                return response ?? new List<BoykotFirma>();
            }
            catch
            {
                return new List<BoykotFirma>();
            }
        }

        public async Task<BoykotFirma?> GetFirmaAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<BoykotFirma>($"{_baseUrl}/firmalar/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> CreateFirmaAsync(BoykotFirma firma)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/firmalar", firma);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateFirmaAsync(int id, BoykotFirma firma)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/firmalar/{id}", firma);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteFirmaAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_baseUrl}/firmalar/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Ürün işlemleri
        public async Task<List<BoykotUrun>> GetUrunlerAsync()
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<List<BoykotUrun>>($"{_baseUrl}/urunler");
                return response ?? new List<BoykotUrun>();
            }
            catch
            {
                return new List<BoykotUrun>();
            }
        }

        public async Task<BoykotUrun?> GetUrunAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<BoykotUrun>($"{_baseUrl}/urunler/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> CreateUrunAsync(BoykotUrun urun)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/urunler", urun);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateUrunAsync(int id, BoykotUrun urun)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/urunler/{id}", urun);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteUrunAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_baseUrl}/urunler/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        // Haber işlemleri
        public async Task<List<BoykotHaber>> GetHaberlerAsync()
        {
            try
            {
                var response = await _httpClient.GetFromJsonAsync<List<BoykotHaber>>($"{_baseUrl}/haberler");
                return response ?? new List<BoykotHaber>();
            }
            catch
            {
                return new List<BoykotHaber>();
            }
        }

        public async Task<BoykotHaber?> GetHaberAsync(int id)
        {
            try
            {
                return await _httpClient.GetFromJsonAsync<BoykotHaber>($"{_baseUrl}/haberler/{id}");
            }
            catch
            {
                return null;
            }
        }

        public async Task<bool> CreateHaberAsync(BoykotHaber haber)
        {
            try
            {
                var response = await _httpClient.PostAsJsonAsync($"{_baseUrl}/haberler", haber);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateHaberAsync(int id, BoykotHaber haber)
        {
            try
            {
                var response = await _httpClient.PutAsJsonAsync($"{_baseUrl}/haberler/{id}", haber);
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteHaberAsync(int id)
        {
            try
            {
                var response = await _httpClient.DeleteAsync($"{_baseUrl}/haberler/{id}");
                return response.IsSuccessStatusCode;
            }
            catch
            {
                return false;
            }
        }
    }
}
