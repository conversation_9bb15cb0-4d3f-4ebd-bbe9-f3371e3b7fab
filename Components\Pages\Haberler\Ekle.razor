@page "/haberler/ekle"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>Haber Ekle - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-plus me-2"></i>
                <PERSON><PERSON>
            </h1>
            <a href="/haberler" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                G<PERSON>
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="card shadow">
                <div class="card-body">
                    <EditForm Model="@haber" OnValidSubmit="@HandleSubmit">
                        <DataAnnotationsValidator />
                        
                        <div class="mb-3">
                            <label for="baslik" class="form-label">Başlık <span class="text-danger">*</span></label>
                            <InputText @bind-Value="haber.Baslik" 
                                       class="form-control" 
                                       id="baslik" 
                                       placeholder="Haber başlığını girin" />
                            <ValidationMessage For="@(() => haber.Baslik)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="icerik" class="form-label">İçerik <span class="text-danger">*</span></label>
                            <InputTextArea @bind-Value="haber.Icerik" 
                                           class="form-control" 
                                           id="icerik" 
                                           rows="10"
                                           placeholder="Haber içeriğini girin" />
                            <ValidationMessage For="@(() => haber.Icerik)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="tarih" class="form-label">Tarih <span class="text-danger">*</span></label>
                            <InputDate @bind-Value="haber.Tarih" 
                                       class="form-control" 
                                       id="tarih" />
                            <ValidationMessage For="@(() => haber.Tarih)" class="text-danger" />
                            <div class="form-text">Haber yayınlanma tarihi</div>
                        </div>

                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @errorMessage
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(successMessage))
                        {
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                @successMessage
                            </div>
                        }

                        <div class="d-flex justify-content-end gap-2">
                            <a href="/haberler" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                İptal
                            </a>
                            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>Kaydediliyor...</span>
                                }
                                else
                                {
                                    <i class="fas fa-save me-2"></i>
                                    <span>Kaydet</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private BoykotHaber haber = new() { Tarih = DateTime.Now };
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;

    private async Task HandleSubmit()
    {
        isLoading = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;
        StateHasChanged();

        try
        {
            var success = await ApiService.CreateHaberAsync(haber);
            
            if (success)
            {
                successMessage = "Haber başarıyla eklendi!";
                haber = new BoykotHaber { Tarih = DateTime.Now }; // Formu temizle
                
                // 2 saniye sonra haberler sayfasına yönlendir
                await Task.Delay(2000);
                Navigation.NavigateTo("/haberler");
            }
            else
            {
                errorMessage = "Haber eklenirken bir hata oluştu.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Bir hata oluştu: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
