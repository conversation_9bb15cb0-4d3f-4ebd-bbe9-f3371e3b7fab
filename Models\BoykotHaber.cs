using System.ComponentModel.DataAnnotations;

namespace BoycottAppAdminPanel.Models
{
    public class BoykotHaber
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Başlık gereklidir")]
        [StringLength(200, ErrorMessage = "<PERSON>şlık en fazla 200 karakter olabilir")]
        public string Baslik { get; set; } = string.Empty;

        [Required(ErrorMessage = "İçerik gereklidir")]
        [StringLength(5000, ErrorMessage = "İçerik en fazla 5000 karakter olabilir")]
        public string Icerik { get; set; } = string.Empty;

        [Required(ErrorMessage = "Tarih gereklidir")]
        public DateTime Tarih { get; set; }
    }
}
