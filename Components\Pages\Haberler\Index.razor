@page "/haberler"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle><PERSON><PERSON>ler - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-newspaper me-2"></i>
                Haberler
            </h1>
            <a href="/haberler/ekle" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                <PERSON>ni <PERSON>
            </a>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    }
    else
    {
        <div class="card shadow">
            <div class="card-body">
                @if (haberler.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Başlık</th>
                                    <th>İçerik</th>
                                    <th>Tarih</th>
                                    <th width="150">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var haber in haberler.OrderByDescending(h => h.Tarih))
                                {
                                    <tr>
                                        <td>@haber.Id</td>
                                        <td>
                                            <strong>@haber.Baslik</strong>
                                        </td>
                                        <td>
                                            @if (haber.Icerik.Length > 100)
                                            {
                                                <span>@(haber.Icerik.Substring(0, 100))...</span>
                                            }
                                            else
                                            {
                                                <span>@haber.Icerik</span>
                                            }
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                @haber.Tarih.ToString("dd.MM.yyyy HH:mm")
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/haberler/duzenle/@haber.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteHaber(haber.Id)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz haber bulunmuyor</h5>
                        <p class="text-muted">İlk haberi eklemek için yukarıdaki "Yeni Haber" butonunu kullanın.</p>
                    </div>
                }
            </div>
        </div>
    }
</div>

@if (showDeleteModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Haber Sil</h5>
                    <button type="button" class="btn-close" @onclick="() => showDeleteModal = false"></button>
                </div>
                <div class="modal-body">
                    <p>Bu haberi silmek istediğinizden emin misiniz?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="() => showDeleteModal = false">İptal</button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmDelete">
                        <i class="fas fa-trash me-2"></i>
                        Sil
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<BoykotHaber> haberler = new();
    private bool isLoading = true;
    private bool showDeleteModal = false;
    private int deleteHaberId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadHaberler();
    }

    private async Task LoadHaberler()
    {
        isLoading = true;
        try
        {
            haberler = await ApiService.GetHaberlerAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Haberler yüklenirken hata: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void DeleteHaber(int id)
    {
        deleteHaberId = id;
        showDeleteModal = true;
    }

    private async Task ConfirmDelete()
    {
        showDeleteModal = false;
        
        try
        {
            var success = await ApiService.DeleteHaberAsync(deleteHaberId);
            if (success)
            {
                await LoadHaberler();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Haber silinirken hata: {ex.Message}");
        }
    }
}
