@page "/firmalar/duzenle/{id:int}"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>Firma Düzenle - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2"></i>
                Firma Düzenle
            </h1>
            <a href="/firmalar" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                Geri <PERSON>ö<PERSON>
            </a>
        </div>
    </div>

    @if (isLoadingData)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    }
    else if (firma == null)
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            Firma bulunamadı.
        </div>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body">
                        <EditForm Model="@firma" OnValidSubmit="@HandleSubmit">
                            <DataAnnotationsValidator />
                            
                            <div class="mb-3">
                                <label for="ad" class="form-label">Firma Adı <span class="text-danger">*</span></label>
                                <InputText @bind-Value="firma.Ad" 
                                           class="form-control" 
                                           id="ad" 
                                           placeholder="Firma adını girin" />
                                <ValidationMessage For="@(() => firma.Ad)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="ulke" class="form-label">Ülke <span class="text-danger">*</span></label>
                                <InputText @bind-Value="firma.Ulke" 
                                           class="form-control" 
                                           id="ulke" 
                                           placeholder="Ülke adını girin" />
                                <ValidationMessage For="@(() => firma.Ulke)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="logoUrl" class="form-label">Logo URL</label>
                                <InputText @bind-Value="firma.LogoUrl" 
                                           class="form-control" 
                                           id="logoUrl" 
                                           placeholder="Logo URL'sini girin" />
                                <ValidationMessage For="@(() => firma.LogoUrl)" class="text-danger" />
                                <div class="form-text">Logo için geçerli bir URL girin (örn: https://example.com/logo.png)</div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <InputCheckbox @bind-Value="firma.Aktif" class="form-check-input" id="aktif" />
                                    <label class="form-check-label" for="aktif">
                                        Aktif
                                    </label>
                                </div>
                                <div class="form-text">Firma aktif durumunu belirler</div>
                            </div>

                            @if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    @errorMessage
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(successMessage))
                            {
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    @successMessage
                                </div>
                            }

                            <div class="d-flex justify-content-end gap-2">
                                <a href="/firmalar" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    İptal
                                </a>
                                <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                    @if (isLoading)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Güncelleniyor...</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-save me-2"></i>
                                        <span>Güncelle</span>
                                    }
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int Id { get; set; }
    
    private BoykotFirma? firma;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;
    private bool isLoadingData = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadFirma();
    }

    private async Task LoadFirma()
    {
        isLoadingData = true;
        try
        {
            firma = await ApiService.GetFirmaAsync(Id);
        }
        catch (Exception ex)
        {
            errorMessage = $"Firma yüklenirken hata: {ex.Message}";
        }
        finally
        {
            isLoadingData = false;
        }
    }

    private async Task HandleSubmit()
    {
        if (firma == null) return;

        isLoading = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;
        StateHasChanged();

        try
        {
            var success = await ApiService.UpdateFirmaAsync(Id, firma);
            
            if (success)
            {
                successMessage = "Firma başarıyla güncellendi!";
                
                // 2 saniye sonra firmalar sayfasına yönlendir
                await Task.Delay(2000);
                Navigation.NavigateTo("/firmalar");
            }
            else
            {
                errorMessage = "Firma güncellenirken bir hata oluştu.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Bir hata oluştu: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
