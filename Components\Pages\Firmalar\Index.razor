@page "/firmalar"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>Firmalar - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-building me-2"></i>
                Firmalar
            </h1>
            <a href="/firmalar/ekle" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Yeni Firma
            </a>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    }
    else
    {
        <div class="card shadow">
            <div class="card-body">
                @if (firmalar.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Logo</th>
                                    <th>Ad</th>
                                    <th>Ülke</th>
                                    <th>Durum</th>
                                    <th width="150">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var firma in firmalar)
                                {
                                    <tr>
                                        <td>@firma.Id</td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(firma.LogoUrl))
                                            {
                                                <img src="@firma.LogoUrl" alt="@firma.Ad" class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;" />
                                            }
                                            else
                                            {
                                                <div class="bg-light d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                    <i class="fas fa-building text-muted"></i>
                                                </div>
                                            }
                                        </td>
                                        <td>@firma.Ad</td>
                                        <td>@firma.Ulke</td>
                                        <td>
                                            @if (firma.Aktif)
                                            {
                                                <span class="badge bg-success">Aktif</span>
                                            }
                                            else
                                            {
                                                <span class="badge bg-danger">Pasif</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/firmalar/duzenle/@firma.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteFirma(firma.Id)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-building fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz firma bulunmuyor</h5>
                        <p class="text-muted">İlk firmayı eklemek için yukarıdaki "Yeni Firma" butonunu kullanın.</p>
                    </div>
                }
            </div>
        </div>
    }
</div>

@if (showDeleteModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Firma Sil</h5>
                    <button type="button" class="btn-close" @onclick="() => showDeleteModal = false"></button>
                </div>
                <div class="modal-body">
                    <p>Bu firmayı silmek istediğinizden emin misiniz?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="() => showDeleteModal = false">İptal</button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmDelete">
                        <i class="fas fa-trash me-2"></i>
                        Sil
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<BoykotFirma> firmalar = new();
    private bool isLoading = true;
    private bool showDeleteModal = false;
    private int deleteFirmaId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadFirmalar();
    }

    private async Task LoadFirmalar()
    {
        isLoading = true;
        try
        {
            firmalar = await ApiService.GetFirmalarAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Firmalar yüklenirken hata: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void DeleteFirma(int id)
    {
        deleteFirmaId = id;
        showDeleteModal = true;
    }

    private async Task ConfirmDelete()
    {
        showDeleteModal = false;
        
        try
        {
            var success = await ApiService.DeleteFirmaAsync(deleteFirmaId);
            if (success)
            {
                await LoadFirmalar();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Firma silinirken hata: {ex.Message}");
        }
    }
}
