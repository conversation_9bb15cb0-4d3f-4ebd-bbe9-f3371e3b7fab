using BoycottAppAdminPanel.Models;

namespace BoycottAppAdminPanel.Services
{
    public class AuthenticationService
    {
        private readonly ApiService _apiService;
        private Admin? _currentAdmin;
        private bool _isAuthenticated = false;

        public event Action? AuthenticationStateChanged;

        public AuthenticationService(ApiService apiService)
        {
            _apiService = apiService;
        }

        public bool IsAuthenticated => _isAuthenticated;
        public Admin? CurrentAdmin => _currentAdmin;

        public async Task<LoginResponse> LoginAsync(string kullaniciAdi, string sifre)
        {
            var loginRequest = new LoginRequest
            {
                KullaniciAdi = kullaniciAdi,
                Sifre = sifre
            };

            Console.WriteLine($"AuthService: Calling API login for {kullaniciAdi}");
            var response = await _apiService.LoginAsync(loginRequest);
            Console.WriteLine($"AuthService: API response - Success: {response.Success}, Message: {response.Message}");

            if (response.Success && response.Admin != null)
            {
                Console.WriteLine($"AuthService: Setting authenticated user: {response.Admin.KullaniciAdi}");
                _currentAdmin = response.Admin;
                _isAuthenticated = true;
                Console.WriteLine($"AuthService: Authentication state set to: {_isAuthenticated}");
                Console.WriteLine($"AuthService: Invoking AuthenticationStateChanged event");
                AuthenticationStateChanged?.Invoke();
            }
            else
            {
                Console.WriteLine($"AuthService: Login failed - Success: {response.Success}, Admin: {response.Admin}");
            }

            return response;
        }

        public void Logout()
        {
            _currentAdmin = null;
            _isAuthenticated = false;
            AuthenticationStateChanged?.Invoke();
        }
    }
}
