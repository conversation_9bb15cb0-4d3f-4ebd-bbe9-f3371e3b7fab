using BoycottAppAdminPanel.Models;

namespace BoycottAppAdminPanel.Services
{
    public class AuthenticationService
    {
        private readonly ApiService _apiService;
        private Admin? _currentAdmin;
        private bool _isAuthenticated = false;

        public event Action? AuthenticationStateChanged;

        public AuthenticationService(ApiService apiService)
        {
            _apiService = apiService;
        }

        public bool IsAuthenticated => _isAuthenticated;
        public Admin? CurrentAdmin => _currentAdmin;

        public async Task<LoginResponse> LoginAsync(string kullaniciAdi, string sifre)
        {
            var loginRequest = new LoginRequest
            {
                KullaniciAdi = kullaniciAdi,
                Sifre = sifre
            };

            var response = await _apiService.LoginAsync(loginRequest);
            
            if (response.Success && response.Admin != null)
            {
                _currentAdmin = response.Admin;
                _isAuthenticated = true;
                AuthenticationStateChanged?.Invoke();
            }

            return response;
        }

        public void Logout()
        {
            _currentAdmin = null;
            _isAuthenticated = false;
            AuthenticationStateChanged?.Invoke();
        }
    }
}
