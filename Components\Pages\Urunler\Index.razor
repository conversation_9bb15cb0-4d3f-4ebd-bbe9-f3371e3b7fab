@page "/urunler"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle><PERSON><PERSON><PERSON><PERSON><PERSON> - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-box me-2"></i>
                Ürünler
            </h1>
            <a href="/urunler/ekle" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                <PERSON><PERSON>
            </a>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    }
    else
    {
        <div class="card shadow">
            <div class="card-body">
                @if (urunler.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Ürün Adı</th>
                                    <th>Firma</th>
                                    <th>Kategori</th>
                                    <th width="150">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var urun in urunler)
                                {
                                    <tr>
                                        <td>@urun.Id</td>
                                        <td>@urun.Ad</td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(urun.FirmaAdi))
                                            {
                                                <span class="badge bg-info">@urun.FirmaAdi</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Firma ID: @urun.FirmaId</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(urun.KategoriAdi))
                                            {
                                                <span class="badge bg-secondary">@urun.KategoriAdi</span>
                                            }
                                            else
                                            {
                                                <span class="text-muted">Kategori ID: @urun.KategoriId</span>
                                            }
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/urunler/duzenle/@urun.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteUrun(urun.Id)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-box fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz ürün bulunmuyor</h5>
                        <p class="text-muted">İlk ürünü eklemek için yukarıdaki "Yeni Ürün" butonunu kullanın.</p>
                    </div>
                }
            </div>
        </div>
    }
</div>

@if (showDeleteModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Ürün Sil</h5>
                    <button type="button" class="btn-close" @onclick="() => showDeleteModal = false"></button>
                </div>
                <div class="modal-body">
                    <p>Bu ürünü silmek istediğinizden emin misiniz?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="() => showDeleteModal = false">İptal</button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmDelete">
                        <i class="fas fa-trash me-2"></i>
                        Sil
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<BoykotUrun> urunler = new();
    private List<BoykotFirma> firmalar = new();
    private List<BoykotKategori> kategoriler = new();
    private bool isLoading = true;
    private bool showDeleteModal = false;
    private int deleteUrunId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        isLoading = true;
        try
        {
            // Paralel olarak tüm verileri yükle
            var urunlerTask = ApiService.GetUrunlerAsync();
            var firmalarTask = ApiService.GetFirmalarAsync();
            var kategorilerTask = ApiService.GetKategorilerAsync();

            await Task.WhenAll(urunlerTask, firmalarTask, kategorilerTask);

            urunler = await urunlerTask;
            firmalar = await firmalarTask;
            kategoriler = await kategorilerTask;

            // Ürünlere firma ve kategori adlarını ekle
            foreach (var urun in urunler)
            {
                var firma = firmalar.FirstOrDefault(f => f.Id == urun.FirmaId);
                var kategori = kategoriler.FirstOrDefault(k => k.Id == urun.KategoriId);
                
                urun.FirmaAdi = firma?.Ad;
                urun.KategoriAdi = kategori?.Ad;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Veriler yüklenirken hata: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void DeleteUrun(int id)
    {
        deleteUrunId = id;
        showDeleteModal = true;
    }

    private async Task ConfirmDelete()
    {
        showDeleteModal = false;
        
        try
        {
            var success = await ApiService.DeleteUrunAsync(deleteUrunId);
            if (success)
            {
                await LoadData();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Ürün silinirken hata: {ex.Message}");
        }
    }
}
