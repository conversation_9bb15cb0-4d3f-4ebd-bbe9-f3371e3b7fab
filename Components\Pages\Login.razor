@page "/login"
@using BoycottAppAdminPanel.Services
@using BoycottAppAdminPanel.Models
@inject AuthenticationService AuthService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<div class="container-fluid vh-100 d-flex align-items-center justify-content-center bg-light">
    <div class="card shadow" style="width: 400px;">
        <div class="card-header text-center bg-primary text-white">
            <h4 class="mb-0">
                <i class="fas fa-shield-alt me-2"></i>
                Admin Paneli
            </h4>
        </div>
        <div class="card-body p-4">
            <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin">
                <DataAnnotationsValidator />
                
                <div class="mb-3">
                    <label for="kullaniciAdi" class="form-label">Kullanıcı Adı</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <InputText @bind-Value="loginModel.KullaniciAdi" 
                                   class="form-control" 
                                   id="kullaniciAdi" 
                                   placeholder="Kullanıcı adınızı girin" />
                    </div>
                    <ValidationMessage For="@(() => loginModel.KullaniciAdi)" class="text-danger" />
                </div>

                <div class="mb-3">
                    <label for="sifre" class="form-label">Şifre</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock"></i>
                        </span>
                        <InputText @bind-Value="loginModel.Sifre" 
                                   type="password" 
                                   class="form-control" 
                                   id="sifre" 
                                   placeholder="Şifrenizi girin" />
                    </div>
                    <ValidationMessage For="@(() => loginModel.Sifre)" class="text-danger" />
                </div>

                @if (!string.IsNullOrEmpty(errorMessage))
                {
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        @errorMessage
                    </div>
                }

                <div class="d-grid">
                    <button type="submit" class="btn btn-primary" disabled="@isLoading">
                        @if (isLoading)
                        {
                            <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            <span>Giriş yapılıyor...</span>
                        }
                        else
                        {
                            <i class="fas fa-sign-in-alt me-2"></i>
                            <span>Giriş Yap</span>
                        }
                    </button>
                </div>
            </EditForm>
        </div>
        <div class="card-footer text-center text-muted">
            <small>Boykot Uygulaması Admin Paneli</small>
        </div>
    </div>
</div>

@code {
    private LoginRequest loginModel = new() { KullaniciAdi = "admin", Sifre = "admin123" };
    private string errorMessage = string.Empty;
    private bool isLoading = false;

    protected override void OnInitialized()
    {
        if (AuthService.IsAuthenticated)
        {
            Navigation.NavigateTo("/");
        }
    }

    private async Task HandleLogin()
    {
        isLoading = true;
        errorMessage = string.Empty;
        StateHasChanged();

        try
        {
            Console.WriteLine($"Login attempt: {loginModel.KullaniciAdi} / {loginModel.Sifre}");
            var response = await AuthService.LoginAsync(loginModel.KullaniciAdi, loginModel.Sifre);
            Console.WriteLine($"Login response: Success={response.Success}, Message={response.Message}");

            if (response.Success)
            {
                Console.WriteLine("Login successful, navigating to home");
                Navigation.NavigateTo("/");
            }
            else
            {
                errorMessage = response.Message;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Login exception: {ex}");
            errorMessage = $"Bir hata oluştu: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
