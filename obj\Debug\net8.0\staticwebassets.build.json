{"Version": 1, "Hash": "R/203tmTzTkckCljzeNEQLYAltUccu6Y/Y7+0veDbh4=", "Source": "BoycottAppAdminPanel", "BasePath": "_content/BoycottAppAdminPanel", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "BoycottAppAdminPanel\\wwwroot", "Source": "BoycottAppAdminPanel", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\", "BasePath": "_content/BoycottAppAdminPanel", "Pattern": "**"}], "Assets": [{"Identity": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\obj\\Debug\\net8.0\\scopedcss\\bundle\\BoycottAppAdminPanel.styles.css", "SourceId": "BoycottAppAdminPanel", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "_content/BoycottAppAdminPanel", "RelativePath": "BoycottAppAdminPanel.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\obj\\Debug\\net8.0\\scopedcss\\bundle\\BoycottAppAdminPanel.styles.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\BoycottAppAdminPanel.bundle.scp.css", "SourceId": "BoycottAppAdminPanel", "SourceType": "Computed", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "_content/BoycottAppAdminPanel", "RelativePath": "BoycottAppAdminPanel.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\BoycottAppAdminPanel.bundle.scp.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\app.css", "SourceId": "BoycottAppAdminPanel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\", "BasePath": "_content/BoycottAppAdminPanel", "RelativePath": "app.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\app.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\bootstrap\\bootstrap.min.css", "SourceId": "BoycottAppAdminPanel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\", "BasePath": "_content/BoycottAppAdminPanel", "RelativePath": "bootstrap/bootstrap.min.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\bootstrap\\bootstrap.min.css.map", "SourceId": "BoycottAppAdminPanel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\", "BasePath": "_content/BoycottAppAdminPanel", "RelativePath": "bootstrap/bootstrap.min.css.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\bootstrap\\bootstrap.min.css.map"}, {"Identity": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\favicon.png", "SourceId": "BoycottAppAdminPanel", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\Desktop\\Balzor Apps\\BoycottApp\\BoycottAppAdminPanel\\wwwroot\\", "BasePath": "_content/BoycottAppAdminPanel", "RelativePath": "favicon.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "PreferTarget", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.png"}]}