using System.ComponentModel.DataAnnotations;

namespace BoycottAppAdminPanel.Models
{
    public class BoykotFirma
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Firma adı gereklidir")]
        [StringLength(100, ErrorMessage = "Firma adı en fazla 100 karakter olabilir")]
        public string Ad { get; set; } = string.Empty;

        [Required(ErrorMessage = "Ülke gereklidir")]
        [StringLength(50, ErrorMessage = "Ülke adı en fazla 50 karakter olabilir")]
        public string Ulke { get; set; } = string.Empty;

        public bool Aktif { get; set; }

        [Url(ErrorMessage = "Geçerli bir URL girin")]
        [StringLength(500, ErrorMessage = "Logo URL en fazla 500 karakter olabilir")]
        public string LogoUrl { get; set; } = string.Empty;
    }
}
