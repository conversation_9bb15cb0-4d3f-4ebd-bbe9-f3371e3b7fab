@page "/kategoriler/duzenle/{id:int}"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle><PERSON><PERSON><PERSON> - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-edit me-2"></i>
                Kategori Düzenle
            </h1>
            <a href="/kategoriler" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                <PERSON><PERSON>
            </a>
        </div>
    </div>

    @if (isLoadingData)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    }
    else if (kategori == null)
    {
        <div class="alert alert-danger" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <PERSON><PERSON><PERSON> bulunamadı.
        </div>
    }
    else
    {
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow">
                    <div class="card-body">
                        <EditForm Model="@kategori" OnValidSubmit="@HandleSubmit">
                            <DataAnnotationsValidator />
                            
                            <div class="mb-3">
                                <label for="ad" class="form-label">Kategori Adı <span class="text-danger">*</span></label>
                                <InputText @bind-Value="kategori.Ad" 
                                           class="form-control" 
                                           id="ad" 
                                           placeholder="Kategori adını girin" />
                                <ValidationMessage For="@(() => kategori.Ad)" class="text-danger" />
                            </div>

                            <div class="mb-3">
                                <label for="aciklama" class="form-label">Açıklama</label>
                                <InputTextArea @bind-Value="kategori.Aciklama" 
                                               class="form-control" 
                                               id="aciklama" 
                                               rows="4"
                                               placeholder="Kategori açıklamasını girin" />
                                <ValidationMessage For="@(() => kategori.Aciklama)" class="text-danger" />
                            </div>

                            @if (!string.IsNullOrEmpty(errorMessage))
                            {
                                <div class="alert alert-danger" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    @errorMessage
                                </div>
                            }

                            @if (!string.IsNullOrEmpty(successMessage))
                            {
                                <div class="alert alert-success" role="alert">
                                    <i class="fas fa-check-circle me-2"></i>
                                    @successMessage
                                </div>
                            }

                            <div class="d-flex justify-content-end gap-2">
                                <a href="/kategoriler" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>
                                    İptal
                                </a>
                                <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                    @if (isLoading)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                        <span>Güncelleniyor...</span>
                                    }
                                    else
                                    {
                                        <i class="fas fa-save me-2"></i>
                                        <span>Güncelle</span>
                                    }
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter] public int Id { get; set; }
    
    private BoykotKategori? kategori;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;
    private bool isLoadingData = true;

    protected override async Task OnInitializedAsync()
    {
        await LoadKategori();
    }

    private async Task LoadKategori()
    {
        isLoadingData = true;
        try
        {
            kategori = await ApiService.GetKategoriAsync(Id);
        }
        catch (Exception ex)
        {
            errorMessage = $"Kategori yüklenirken hata: {ex.Message}";
        }
        finally
        {
            isLoadingData = false;
        }
    }

    private async Task HandleSubmit()
    {
        if (kategori == null) return;

        isLoading = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;
        StateHasChanged();

        try
        {
            var success = await ApiService.UpdateKategoriAsync(Id, kategori);
            
            if (success)
            {
                successMessage = "Kategori başarıyla güncellendi!";
                
                // 2 saniye sonra kategoriler sayfasına yönlendir
                await Task.Delay(2000);
                Navigation.NavigateTo("/kategoriler");
            }
            else
            {
                errorMessage = "Kategori güncellenirken bir hata oluştu.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Bir hata oluştu: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
