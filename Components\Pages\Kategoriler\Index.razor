@page "/kategoriler"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>Kategoriler - Boykot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>
                Kategoriler
            </h1>
            <a href="/kategoriler/ekle" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>
                Yeni <PERSON>
            </a>
        </div>
    </div>

    @if (isLoading)
    {
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Yükleniyor...</span>
            </div>
        </div>
    }
    else
    {
        <div class="card shadow">
            <div class="card-body">
                @if (kategoriler.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Ad</th>
                                    <th>Açıklama</th>
                                    <th width="150">İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var kategori in kategoriler)
                                {
                                    <tr>
                                        <td>@kategori.Id</td>
                                        <td>@kategori.Ad</td>
                                        <td>@kategori.Aciklama</td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="/kategoriler/duzenle/@kategori.Id" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-outline-danger" @onclick="() => DeleteKategori(kategori.Id)">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz kategori bulunmuyor</h5>
                        <p class="text-muted">İlk kategoriyi eklemek için yukarıdaki "Yeni Kategori" butonunu kullanın.</p>
                    </div>
                }
            </div>
        </div>
    }
</div>

@if (showDeleteModal)
{
    <div class="modal fade show d-block" tabindex="-1" style="background-color: rgba(0,0,0,0.5);">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Kategori Sil</h5>
                    <button type="button" class="btn-close" @onclick="() => showDeleteModal = false"></button>
                </div>
                <div class="modal-body">
                    <p>Bu kategoriyi silmek istediğinizden emin misiniz?</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" @onclick="() => showDeleteModal = false">İptal</button>
                    <button type="button" class="btn btn-danger" @onclick="ConfirmDelete">
                        <i class="fas fa-trash me-2"></i>
                        Sil
                    </button>
                </div>
            </div>
        </div>
    </div>
}

@code {
    private List<BoykotKategori> kategoriler = new();
    private bool isLoading = true;
    private bool showDeleteModal = false;
    private int deleteKategoriId = 0;

    protected override async Task OnInitializedAsync()
    {
        await LoadKategoriler();
    }

    private async Task LoadKategoriler()
    {
        isLoading = true;
        try
        {
            kategoriler = await ApiService.GetKategorilerAsync();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Kategoriler yüklenirken hata: {ex.Message}");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void DeleteKategori(int id)
    {
        deleteKategoriId = id;
        showDeleteModal = true;
    }

    private async Task ConfirmDelete()
    {
        showDeleteModal = false;
        
        try
        {
            var success = await ApiService.DeleteKategoriAsync(deleteKategoriId);
            if (success)
            {
                await LoadKategoriler();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Kategori silinirken hata: {ex.Message}");
        }
    }
}
