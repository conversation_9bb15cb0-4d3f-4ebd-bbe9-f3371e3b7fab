@page "/kategoriler/ekle"
@inject ApiService ApiService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle><PERSON><PERSON><PERSON> - <PERSON>kot Admin</PageTitle>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h1 class="h3 mb-0">
                <i class="fas fa-plus me-2"></i>
                Yeni <PERSON>
            </h1>
            <a href="/kategoriler" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>
                G<PERSON>
            </a>
        </div>
    </div>

    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-body">
                    <EditForm Model="@kategori" OnValidSubmit="@HandleSubmit">
                        <DataAnnotationsValidator />
                        
                        <div class="mb-3">
                            <label for="ad" class="form-label"><PERSON><PERSON><PERSON> <span class="text-danger">*</span></label>
                            <InputText @bind-Value="kategori.Ad" 
                                       class="form-control" 
                                       id="ad" 
                                       placeholder="Kategori adını girin" />
                            <ValidationMessage For="@(() => kategori.Ad)" class="text-danger" />
                        </div>

                        <div class="mb-3">
                            <label for="aciklama" class="form-label">Açıklama</label>
                            <InputTextArea @bind-Value="kategori.Aciklama" 
                                           class="form-control" 
                                           id="aciklama" 
                                           rows="4"
                                           placeholder="Kategori açıklamasını girin" />
                            <ValidationMessage For="@(() => kategori.Aciklama)" class="text-danger" />
                        </div>

                        @if (!string.IsNullOrEmpty(errorMessage))
                        {
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                @errorMessage
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(successMessage))
                        {
                            <div class="alert alert-success" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                @successMessage
                            </div>
                        }

                        <div class="d-flex justify-content-end gap-2">
                            <a href="/kategoriler" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>
                                İptal
                            </a>
                            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <span>Kaydediliyor...</span>
                                }
                                else
                                {
                                    <i class="fas fa-save me-2"></i>
                                    <span>Kaydet</span>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private BoykotKategori kategori = new();
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool isLoading = false;

    private async Task HandleSubmit()
    {
        isLoading = true;
        errorMessage = string.Empty;
        successMessage = string.Empty;
        StateHasChanged();

        try
        {
            var success = await ApiService.CreateKategoriAsync(kategori);
            
            if (success)
            {
                successMessage = "Kategori başarıyla eklendi!";
                kategori = new BoykotKategori(); // Formu temizle
                
                // 2 saniye sonra kategoriler sayfasına yönlendir
                await Task.Delay(2000);
                Navigation.NavigateTo("/kategoriler");
            }
            else
            {
                errorMessage = "Kategori eklenirken bir hata oluştu.";
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Bir hata oluştu: {ex.Message}";
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}
